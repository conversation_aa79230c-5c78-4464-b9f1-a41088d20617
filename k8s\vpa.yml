apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: messages-integration-service-vpa
  labels:
    app: messages-integration-service
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: messages-integration-service-deployment
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: messages-integration-service
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 1000m
        memory: 1Gi
      controlledResources: ["cpu", "memory"]
