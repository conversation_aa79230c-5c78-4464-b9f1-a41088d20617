namespace Domain.WhatsApp.DTOs;

public class Hash
{
    public string? Apikey { get; set; }
}

public class Instance
{
    public string? InstanceName { get; set; }
    public string? InstanceId { get; set; }
    public string? WebhookWaBusiness { get; set; }
    public string? AccessTokenWaBusiness { get; set; }
    public string? Status { get; set; }
}

public class CreateInstanceResponse
{
    public Instance Instance { get; set; } = new();
    public Hash Hash { get; set; } = new();
    public Settings Settings { get; set; } = new();
}

public class Settings
{
    public bool RejectCall { get; set; }
    public string? MsgCall { get; set; }
    public bool GroupsIgnore { get; set; }
    public bool AlwaysOnline { get; set; }
    public bool ReadMessages { get; set; }
    public bool ReadStatus { get; set; }
    public bool SyncFullHistory { get; set; }
}
