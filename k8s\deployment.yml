apiVersion: apps/v1
kind: Deployment
metadata:
  name: messages-integration-service-deployment
  labels:
    app: messages-integration-service
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: messages-integration-service
  template:
    metadata:
      labels:
        app: messages-integration-service
        version: v1
    spec:
      containers:
      - name: messages-integration-service
        image: gcr.io/highcapital-470117/messages-integration-service:${IMAGE_TAG}
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: ASPNETCORE_URLS
          value: "http://+:8080"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /docs
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /docs
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        imagePullPolicy: Always
      restartPolicy: Always
