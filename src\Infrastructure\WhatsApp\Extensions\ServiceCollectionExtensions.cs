using Domain.WhatsApp.Interfaces;
using Infrastructure.WhatsApp.Configuration;
using Infrastructure.WhatsApp.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.WhatsApp.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddWhatsAppServices(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        // Configuração
        services.Configure<EvolutionApiSettings>(
            configuration.GetSection(EvolutionApiSettings.SectionName)
        );

        // HttpClient para Evolution API
        services.AddHttpClient<IEvolutionApiService, EvolutionApiService>(
            (serviceProvider, client) =>
            {
                var settings = configuration
                    .GetSection(EvolutionApiSettings.SectionName)
                    .Get<EvolutionApiSettings>();
                if (settings != null)
                {
                    client.BaseAddress = new Uri(settings.BaseUrl);
                    if (!string.IsNullOrEmpty(settings.GlobalApiKey))
                    {
                        client.DefaultRequestHeaders.Add("apikey", settings.GlobalApiKey);
                    }
                }
            }
        );

        // Serviços
        services.AddScoped<IEvolutionApiService, EvolutionApiService>();
        services.AddScoped<IWhatsAppIntegrationService, WhatsAppIntegrationService>();

        return services;
    }
}
