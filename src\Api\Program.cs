using HighCapital.Core.Dependencies;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.AddInfrastructureServices();
builder.AddApplicationServices();
builder.AddWebServices();
builder.Services.AddInfrastructure(builder.Configuration);
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddCoreCorsPolicy();

var app = builder.Build();

// Enable Swagger and Scalar
app.UseOpenApiAndScalarDocumentation();
app.UseCoreCorsPolicy();
app.UseExceptionHandler();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();

public partial class Program { }
