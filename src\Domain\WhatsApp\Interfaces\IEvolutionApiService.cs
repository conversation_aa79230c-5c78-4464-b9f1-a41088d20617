using Domain.WhatsApp.DTOs;

namespace Domain.WhatsApp.Interfaces;

public interface IEvolutionApiService
{
    Task<SendMessageResponse> SendTextMessageAsync(string instanceName, SendMessageRequest request, CancellationToken cancellationToken = default);
    Task<QrCodeResponse> GetQrCodeAsync(string instanceName, CancellationToken cancellationToken = default);
    Task<ConnectionStatusResponse> GetConnectionStatusAsync(string instanceName, CancellationToken cancellationToken = default);
}
