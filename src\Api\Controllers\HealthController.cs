using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly ILogger<HealthController> _logger;

    public HealthController(ILogger<HealthController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Simple health check endpoint for testing debugging
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet]
    public IActionResult Get()
    {
        _logger.LogInformation("Health check endpoint called at {Time}", DateTime.UtcNow);

        // This is a good place to set a breakpoint for testing
        var response = new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
        };

        return Ok(response);
    }

    /// <summary>
    /// Debug endpoint for testing breakpoints
    /// </summary>
    /// <returns>Debug information</returns>
    [HttpGet("debug")]
    public IActionResult Debug()
    {
        _logger.LogInformation("Debug endpoint called");

        // Set a breakpoint here to test debugging
        var debugInfo = new
        {
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            WorkingDirectory = Environment.CurrentDirectory,
            DotNetVersion = Environment.Version.ToString(),
            OSVersion = Environment.OSVersion.ToString(),
        };

        _logger.LogInformation("Debug info collected: {@DebugInfo}", debugInfo);

        return Ok(debugInfo);
    }
}
