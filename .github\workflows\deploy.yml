name: Deploy to GKE

on:
  push:
    branches:
      - release

env:
  PROJECT_ID: highcapital-470117
  GKE_CLUSTER: cluster-high-capital-dev
  GKE_ZONE: us-east1-b
  DEPLOYMENT_NAME: messages-integration-service-deployment
  IMAGE: messages-integration-service

jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest
    environment: production

    permissions:
      contents: read
      id-token: write

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure Google Cloud CLI
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2

    - name: Configure Docker to use gcloud as a credential helper
      run: |-
        gcloud --quiet auth configure-docker

    - name: Install kubectl
      run: |-
        gcloud components install kubectl 

    - name: Get the GKE credentials so we can deploy to the cluster
      run: |-
        gcloud container clusters get-credentials "$GKE_CLUSTER" --zone "$GKE_ZONE"

    - name: Build the Docker image
      run: |-
        docker build -t "gcr.io/$PROJECT_ID/$IMAGE:$GITHUB_SHA" \
          --build-arg NUGET_USERNAME="${{ secrets.NUGET_USERNAME }}" \
          --build-arg NUGET_TOKEN="${{ secrets.NUGET_TOKEN }}" .

    - name: Push the Docker image to Google Container Registry
      run: |-
        docker push "gcr.io/$PROJECT_ID/$IMAGE:$GITHUB_SHA"
    
    - name: Set IMAGE_TAG
      run: echo "IMAGE_TAG=${GITHUB_SHA}" >> $GITHUB_ENV

    - name: Render deployment file
      run: envsubst < k8s/deployment.yml > k8s/deployment_rendered.yml

    - name: Set up Kustomize
      run: |-
        curl -sfLo kustomize https://github.com/kubernetes-sigs/kustomize/releases/download/v3.1.0/kustomize_3.1.0_linux_amd64
        chmod u+x ./kustomize

    - name: Deploy to GKE
      run: |-
        # Replace the image name in the k8s template
        ./kustomize edit set image gcr.io/PROJECT_ID/IMAGE:TAG=gcr.io/$PROJECT_ID/$IMAGE:$GITHUB_SHA
        ./kustomize build . | kubectl apply -f -
        kubectl rollout status deployment/$DEPLOYMENT_NAME
        kubectl get services -o wide
