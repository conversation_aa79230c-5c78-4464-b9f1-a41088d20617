using Application.WhatsApp.Services;
using Domain.WhatsApp.Interfaces;
using HighAgentsApi.Api.Helpers;
using HighCapital.Core.Dependencies;
using Microsoft.AspNetCore.Mvc;


namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddWebServices(this IHostApplicationBuilder builder)
    {

        builder.Services.AddOpenApiDocumentation();
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddExceptionHandler<CustomExceptionHandler>();
        builder.Services.AddProblemDetails();


        builder.Services.AddHealthChecks();

        builder.Services.AddScoped<IWhatsAppApplicationService, WhatsAppApplicationService>();

        builder.Services.Configure<ApiBehaviorOptions>(options =>
            options.SuppressModelStateInvalidFilter = true);

        builder.Services.AddEndpointsApiExplorer();


    }
}
